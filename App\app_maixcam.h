// Copyright (c) 2024 �״׵��ӹ�����

#ifndef __APP_MAIXCAM_H
#define __APP_MAIXCAM_H

#include "mydefine.h"

// ����Э�鳣��
#define FRAME_HEADER '$'
#define FRAME_FOOTER '\n'
#define RED_LASER_ID 'R'
#define GREEN_LASER_ID 'G'

// �����������ݽṹ
#ifndef LASERCOORD_T_DEFINED
#define LASERCOORD_T_DEFINED
typedef struct {
    char type;    // ��������: 'R'��ʾ��ɫ���⣬'G'��ʾ��ɫ����
    int x;        // X����
    int y;        // Y����
} LaserCoord_t;
#endif

// ����ص���������
typedef void (*LaserCoordCallback_t)(LaserCoord_t coord);

// MaixCam ������
void maixcam_task(MultiTimer *timer, void *userData);

// ���ݽ�������
int maixcam_parse_data(char *buffer);

// �����������ݻص�����
void maixcam_set_callback(LaserCoordCallback_t callback);

// ȫ������������������ж�
extern int target_x_coord;  // to(x,y)�е�xֵ
extern int target_y_coord;  // to(x,y)�е�yֵ

// ������ж�����
int maixcam_is_target_in_range(int min_x, int max_x, int min_y, int max_y);  // �ж�Ŀ���Ƿ��ڷ�Χ��
int maixcam_get_target_distance_from_center(int center_x, int center_y);     // ��ȡ��������ľ���
void maixcam_check_target_near_point(void);  // �ж�Ŀ���Ƿ����(160,120)

#endif /* __APP_MAIXCAM_H */
